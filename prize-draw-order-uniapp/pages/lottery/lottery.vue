<template>
  <scroll-view class="lottery-container" scroll-y="true" enable-back-to-top="true" :scroll-with-animation="true"
    :style="'background:' + backgroundGradient">
    <!-- 加载状态 -->
    <view class="loading-container" v-if="isLoading">
      <view class="loading-icon">⏳</view>
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 商家信息 -->
    <view class="merchant-header" v-if="merchantInfo && !isLoading">
      <view class="activity-desc" v-if="currentActivity && currentActivity.activityDesc">{{ currentActivity.activityDesc
      }}</view>
    </view>



    <!-- 主要内容区域（非加载状态时显示） -->
    <template v-if="!isLoading">
      <!-- 无活动提示 -->
      <view class="no-activity-tip" v-if="!currentActivity">
        <view class="tip-icon">📢</view>
        <view class="tip-title">暂无抽奖活动</view>
        <view class="tip-desc">当前没有进行中的抽奖活动，请稍后再试</view>
      </view>

      <!-- 九宫格抽奖（有剩余次数时显示） -->
      <view class="lottery-grid-container" v-if="remainingDraws > 0 && currentActivity">
        <view class="grid-wrapper">
          <view class="lottery-grid">
            <view class="grid-item" v-for="(item, index) in gridItems" :key="index"
              :class="{ 'active': currentIndex === index, 'center': index === 4 }"
              :style="index === 4 ? 'background:' + gridContainerBg : ''" @click="index === 4 ? startLottery() : null">
              <view v-if="index === 4" class="center-button">
                <view class="center-text">{{ isDrawing ? '抽奖中...' : '点击抽奖' }}</view>
                <view class="remaining-text">{{ getRemainingText() }}</view>
              </view>
              <view v-else-if="item" class="prize-item">
                <view class="prize-icon">
                  <image v-if="item.prizeImage" :src="getFullImageUrl(item.prizeImage)" class="prize-image"
                    mode="aspectFit" />
                  <text v-else class="default-icon">🎁</text>
                </view>
                <view class="prize-name">{{ item.prizeName }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 无抽奖次数时显示上次中奖记录（页面内显示） -->
      <view class="last-winning-section" v-if="remainingDraws <= 0 && currentActivity && lastWinningRecord">
        <view class="last-winning-card" :style="{ background: 'rgba(255, 255, 255, 0.95)' }">
          <!-- 标题 -->
          <view class="section-title">
            <text class="title-icon">🎉</text>
            <text class="title-text">中奖记录</text>
          </view>

          <!-- 奖品信息 -->
          <view class="prize-info-card">
            <view class="prize-icon-large">
              <image v-if="lastWinningRecord.prizeImage" :src="getFullImageUrl(lastWinningRecord.prizeImage)"
                class="prize-image-large" mode="aspectFit" />
              <text v-else class="default-icon-large">🎁</text>
            </view>
            <view class="prize-details-large">
              <view class="prize-name-large">{{ lastWinningRecord.prizeName }}</view>
              <view class="prize-desc-large" v-if="lastWinningRecord.prizeDesc">{{ lastWinningRecord.prizeDesc }}</view>
              <view class="draw-time-large">
                <text>抽奖时间：{{ formatTime(lastWinningRecord.drawTime) }}</text>
              </view>
              <view class="claim-status"
                :class="{ 'claimed': lastWinningRecord.claimStatus === '1', 'unclaimed': lastWinningRecord.claimStatus === '0' }">
                <text>{{ lastWinningRecord.claimStatus === '1' ? '已领取' : '待领取' }}</text>
              </view>
            </view>
          </view>

          <!-- 领取说明 -->
          <view class="claim-instruction-card" v-if="currentActivity && currentActivity.claimInstruction">
            <view class="instruction-title">领取说明</view>
            <view class="instruction-content">{{ currentActivity.claimInstruction }}</view>
          </view>

          <!-- 微信二维码 -->
          <view class="wechat-qrcode-card" v-if="currentActivity && currentActivity.wechatQrcode">
            <view class="qrcode-title">联系客服</view>
            <image :src="getFullImageUrl(currentActivity.wechatQrcode)" class="qrcode-img-inline" mode="aspectFit"
              @error="handleImageError" @load="handleImageLoad"></image>
            <view class="qrcode-error" v-if="imageLoadError">
              <text>二维码加载失败</text>
            </view>
          </view>

          <!-- 查看更多记录按钮 -->
          <view class="more-records-btn" @click="goToRecords">
            <text>查看更多记录</text>
          </view>
        </view>
      </view>

      <!-- 抽奖规则（只有在有抽奖次数时显示） -->
      <view class="lottery-rules" v-if="remainingDraws > 0 && currentActivity && currentActivity.drawRules">
        <view class="rules-title">抽奖规则</view>
        <view class="rules-content">{{ currentActivity.drawRules }}</view>
      </view>






    </template>
  </scroll-view>
</template>

<script>
import { merchantApi, lotteryApi, configApi, getImageUrl } from '@/utils/api.js'

export default {
  data() {
    return {
      merchantCode: '',
      tableNumber: '',
      merchantInfo: null,
      merchantConfig: {}, // 商家配置
      currentActivity: null,
      prizeList: [],
      remainingDraws: 0,
      drawsInfo: null, // 抽奖次数详情
      isDrawing: false,
      isLoading: true, // 页面加载状态
      userOpenid: '',
      hasDrawn: false,
      lotteryResult: null,
      // 九宫格相关数据
      gridItems: [],
      currentIndex: -1,
      animationTimer: null,
      animationSpeed: 100,
      animationCount: 0,
      targetIndex: -1,

      // 上次中奖记录
      lastWinningRecord: null,

      // 图片加载错误状态
      imageLoadError: false
    }
  },

  computed: {
    // 解析UI配置
    uiConfig() {
      const uiConfigStr = this.merchantConfig.ui_config
      if (uiConfigStr) {
        try {
          return JSON.parse(uiConfigStr)
        } catch (e) {
          console.error('UI配置解析失败:', e)
          return {}
        }
      }
      return {}
    },

    // 主题色彩
    primaryColor() {
      return this.uiConfig.primaryColor || '#667eea'
    },

    // 背景渐变色
    backgroundGradient() {
      const color = this.primaryColor
      // 生成基于主题色的渐变背景
      return `linear-gradient(135deg, ${color} 0%, ${this.adjustColor(color, -20)} 100%)`
    },

    // 九宫格容器背景色
    gridContainerBg() {
      return `linear-gradient(135deg, ${this.primaryColor}, ${this.adjustColor(this.primaryColor, -15)})`
    },

    // 烟花样式数组
    fireworkStyles() {
      const angles = [0, 60, 120, 180, 240, 300]
      return angles.map((angle, index) => ({
        transform: `rotate(${angle}deg)`,
        animationDelay: `${(index + 1) * 0.1}s`
      }))
    }
  },

  watch: {
    // 监听主题色彩变化，动态设置导航栏颜色
    primaryColor: {
      handler(newColor) {
        if (newColor) {
          uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: newColor
          })
        }
      },
      immediate: true
    }
  },

  onLoad(options) {
    this.merchantCode = options.merchantCode || 'system'
    this.tableNumber = options.tableNumber || 'A001'

    this.initPage()
  },


  onUnload() {
    // 页面卸载时清理资源
    this.resetGridStyle()
  },

  methods: {
    async initPage() {
      try {
        this.isLoading = true
        console.log('开始初始化抽奖页面...')

        // 获取用户openid（实际项目中需要通过微信登录获取）
        // 为了测试重复抽奖限制，这里使用固定的测试用户ID
        this.userOpenid = 'test_user_001'

        // 加载商家信息
        console.log('加载商家信息...')
        await this.loadMerchantInfo()

        // 加载商家配置
        console.log('加载商家配置...')
        await this.loadMerchantConfig()

        // 加载当前活动
        console.log('加载当前活动...')
        await this.loadCurrentActivity()

        // 检查用户抽奖状态
        console.log('检查用户抽奖状态...')
        await this.checkUserLotteryStatus()

        // 加载抽奖次数详情
        console.log('加载抽奖次数详情...')
        await this.loadDrawsInfo()

        // 初始化九宫格（有剩余次数时初始化）
        if (this.remainingDraws > 0) {
          console.log('初始化九宫格，剩余次数:', this.remainingDraws)
          this.initGrid()
        } else {
          console.log('无剩余抽奖次数，不初始化九宫格')
        }

        console.log('页面初始化完成')

      } catch (error) {
        console.error('页面初始化失败:', error)
        this.handleError(error)
      } finally {
        this.isLoading = false
      }
    },

    async loadMerchantInfo() {
      try {
        const res = await merchantApi.getMerchantInfo(this.merchantCode)
        if (res.code === 200) {
          this.merchantInfo = res.data
        }
      } catch (error) {
        this.handleError(error)
      }
    },

    async loadMerchantConfig() {
      try {
        const res = await configApi.getAllConfig(this.merchantCode)
        if (res.code === 200) {
          this.merchantConfig = res.data || {}
        }
      } catch (error) {
        console.error('获取商家配置失败:', error)
      }
    },

    async loadCurrentActivity() {
      try {
        const res = await lotteryApi.getCurrentActivity(this.merchantCode)
        if (res.code === 200 && res.data) {
          this.currentActivity = res.data
          this.prizeList = JSON.parse(res.data.prizeConfig || '[]')

          // 动态设置导航栏标题为活动名称
          if (this.currentActivity.activityName) {
            uni.setNavigationBarTitle({
              title: this.currentActivity.activityName
            })
          }

          // 获取剩余抽奖次数
          await this.loadRemainingDraws()
        }
      } catch (error) {
        console.error('获取活动信息失败:', error)
      }
    },

    async loadRemainingDraws() {
      if (!this.currentActivity) return

      try {
        const res = await lotteryApi.getRemainingDraws(this.currentActivity.activityId, this.userOpenid)
        if (res.code === 200) {
          this.remainingDraws = res.data
        }
      } catch (error) {
        console.error('获取剩余次数失败:', error)
      }
    },

    async loadDrawsInfo() {
      if (!this.currentActivity) return

      try {
        const res = await lotteryApi.getDrawsInfo(this.currentActivity.activityId, this.userOpenid)
        if (res.code === 200) {
          this.drawsInfo = res.data
          this.remainingDraws = res.data.remainingDraws
        }
      } catch (error) {
        console.error('获取抽奖次数详情失败:', error)
      }
    },



    async loadLastWinningRecord() {
      try {
        const res = await lotteryApi.getUserWinningRecordsByMerchant(this.merchantCode, this.userOpenid)
        if (res.code === 200) {
          // 查找最近的中奖记录
          const winningRecords = res.data || []
          if (winningRecords.length > 0) {
            // 按时间排序，取最新的中奖记录
            winningRecords.sort((a, b) => new Date(b.drawTime) - new Date(a.drawTime))
            this.lastWinningRecord = winningRecords[0]
            console.log('获取到上次中奖记录:', this.lastWinningRecord)
          } else {
            this.lastWinningRecord = null
            console.log('没有找到中奖记录')
          }
        }
      } catch (error) {
        console.error('获取上次中奖记录失败:', error)
        this.lastWinningRecord = null
      }
    },

    async checkUserLotteryStatus() {
      if (!this.currentActivity) return

      try {
        const res = await lotteryApi.getUserLotteryStatus(this.currentActivity.activityId, this.userOpenid)
        if (res.code === 200) {
          const data = res.data
          this.hasDrawn = data.hasDrawn
          this.remainingDraws = data.remainingDraws

          // 如果用户没有抽奖次数了，获取上次中奖记录用于显示
          if (data.remainingDraws <= 0) {
            await this.loadLastWinningRecord()
          }
        }
      } catch (error) {
        console.error('获取用户抽奖状态失败:', error)
      }
    },

    initGrid() {
      if (this.prizeList.length === 0) return

      // 创建九宫格数据，确保有8个奖品位置（中间是抽奖按钮）
      this.gridItems = []

      // 如果奖品不足8个，用"谢谢参与"填充
      const prizes = [...this.prizeList]
      while (prizes.length < 8) {
        prizes.push({
          prizeName: '谢谢参与',
          prizeType: 'thanks',
          probability: 0
        })
      }

      // 如果奖品超过8个，只取前8个
      if (prizes.length > 8) {
        prizes.splice(8)
      }

      // 九宫格布局：
      // 0  1  2
      // 7  4  3  (4是中心抽奖按钮)
      // 6  5  4
      // 需要创建9个位置，其中index=4是抽奖按钮，其他8个位置放奖品
      for (let i = 0; i < 9; i++) {
        if (i === 4) {
          // 中心位置是抽奖按钮，不需要奖品数据
          this.gridItems.push(null)
        } else {
          // 计算奖品索引：0,1,2,3对应prizes[0,1,2,3]，5,6,7,8对应prizes[4,5,6,7]
          const prizeIndex = i < 4 ? i : i - 1
          if (prizeIndex < prizes.length) {
            this.gridItems.push(prizes[prizeIndex])
          } else {
            this.gridItems.push({
              prizeName: '谢谢参与',
              prizeType: 'thanks',
              probability: 0
            })
          }
        }
      }
    },

    async startLottery() {
      if (this.isDrawing) return
      if (this.remainingDraws <= 0) {
        let message = '抽奖次数已用完'
        if (this.drawsInfo) {
          if (this.drawsInfo.dailyLimit > 0 && this.drawsInfo.dailyRemaining <= 0) {
            message = '今日抽奖次数已用完'
          } else if (this.drawsInfo.totalLimit > 0 && this.drawsInfo.totalRemaining <= 0) {
            message = '总抽奖次数已用完'
          }
        }
        uni.showToast({
          title: message,
          icon: 'none'
        })
        return
      }
      if (!this.currentActivity) {
        uni.showToast({
          title: '暂无可参与的活动',
          icon: 'none'
        })
        return
      }

      this.isDrawing = true
      // 重置九宫格样式，确保每次抽奖都从干净的状态开始
      this.resetGridStyle()

      try {
        const drawData = {
          activityId: this.currentActivity.activityId,
          userOpenid: this.userOpenid,
          userNickname: '用户' + this.userOpenid.slice(-4),
          userAvatar: '',
          tableId: null // 如果有桌台信息可以传入
        }

        const res = await lotteryApi.performDraw(drawData)
        if (res.code === 200) {
          const result = res.data

          // 找到中奖奖品在九宫格中的位置
          let targetIndex = this.gridItems.findIndex(item =>
            item && item.prizeName === result.prizeName
          )

          // 如果没找到，默认停在第一个位置（跳过中心位置）
          if (targetIndex === -1 || targetIndex === 4) {
            targetIndex = 0
          }

          // 开始九宫格动画
          this.startGridAnimation(targetIndex, () => {
            this.showResult(result)
            this.isDrawing = false
            this.lotteryResult = result
            // 重新获取剩余次数和用户状态
            this.loadDrawsInfo().then(() => {
              // 检查抽奖完成后是否还有剩余次数，如果没有则加载上次中奖记录
              if (this.remainingDraws <= 0) {
                this.loadLastWinningRecord()
              }
            })
          })

        } else {
          throw new Error(res.msg || '抽奖失败')
        }
      } catch (error) {
        this.isDrawing = false
        this.handleError(error)
      }
    },

    startGridAnimation(targetIndex, callback) {
      this.targetIndex = targetIndex
      this.animationCount = 0
      this.currentIndex = 0
      this.animationSpeed = 100

      // 九宫格动画顺序：跳过中心位置(index=4)
      // 顺序：0,1,2,3,5,6,7,8 (跳过4)
      const animationOrder = [0, 1, 2, 3, 5, 6, 7, 8]

      // 动画总圈数和最终位置
      const totalRounds = 3 // 转3圈
      const totalSteps = totalRounds * 8 + animationOrder.indexOf(targetIndex)

      const animate = () => {
        const orderIndex = this.animationCount % 8
        this.currentIndex = animationOrder[orderIndex]
        this.animationCount++

        // 动态调整速度，最后几步减速
        if (this.animationCount > totalSteps - 10) {
          this.animationSpeed = 200
        } else if (this.animationCount > totalSteps - 20) {
          this.animationSpeed = 150
        }

        if (this.animationCount >= totalSteps) {
          // 动画结束
          this.currentIndex = targetIndex
          clearTimeout(this.animationTimer)
          setTimeout(callback, 500) // 延迟500ms显示结果
        } else {
          // 继续动画
          this.animationTimer = setTimeout(animate, this.animationSpeed)
        }
      }

      animate()
    },

    showResult(result) {
      const isWinner = result.isWinner === '1'
      const isThanks = result.prizeType === 'thanks' || result.prizeName === '谢谢惠顾' || result.prizeName === '谢谢参与'

      if (isWinner) {
        // 如果中奖，显示Toast提示
        uni.showToast({
          title: `恭喜中奖：${result.prizeName}`,
          icon: 'success',
          duration: 3000
        })
      } else {
        // 如果未中奖，根据类型显示不同提示
        let title = '谢谢参与，再接再厉！'
        if (isThanks) {
          title = '未抽中，谢谢参与！'
        }

        uni.showToast({
          title: title,
          icon: 'none',
          duration: 2000
        })
      }
    },





    // 重置九宫格样式
    resetGridStyle() {
      // 清除当前高亮状态
      this.currentIndex = -1
      // 清除动画定时器
      if (this.animationTimer) {
        clearTimeout(this.animationTimer)
        this.animationTimer = null
      }
      // 重置动画相关状态
      this.animationCount = 0
      this.targetIndex = -1
      this.animationSpeed = 100
    },



    formatTime(timeStr) {
      const date = new Date(timeStr)
      return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
    },

    getRemainingText() {
      if (!this.drawsInfo) {
        return `剩余${this.remainingDraws}次`
      }

      // 如果有每日限制和总限制，显示最小的剩余次数
      if (this.drawsInfo.dailyLimit > 0 && this.drawsInfo.totalLimit > 0) {
        const minRemaining = Math.min(this.drawsInfo.dailyRemaining, this.drawsInfo.totalRemaining)
        return `剩余${minRemaining}次`
      }

      // 如果只有每日限制
      if (this.drawsInfo.dailyLimit > 0) {
        return `今日剩余${this.drawsInfo.dailyRemaining}次`
      }

      // 如果只有总限制
      if (this.drawsInfo.totalLimit > 0) {
        return `总剩余${this.drawsInfo.totalRemaining}次`
      }

      // 如果没有限制
      return '点击抽奖'
    },





    handleError(error) {
      let message = error.message || error.msg || '系统异常'

      // 检查是否是商家到期错误
      if (message.includes('过期') || message.includes('到期')) {
        uni.showModal({
          title: '系统提示',
          content: message,
          showCancel: false,
          confirmText: '我知道了'
        })
      } else {
        uni.showToast({
          title: message,
          icon: 'none',
          duration: 3000
        })
      }
    },

    // 获取完整的图片URL
    getFullImageUrl(imagePath) {
      return getImageUrl(imagePath)
    },

    // 颜色调整工具方法
    adjustColor(color, amount) {
      // 将十六进制颜色转换为RGB
      const hex = color.replace('#', '')
      const r = parseInt(hex.substr(0, 2), 16)
      const g = parseInt(hex.substr(2, 2), 16)
      const b = parseInt(hex.substr(4, 2), 16)

      // 调整亮度
      const newR = Math.max(0, Math.min(255, r + amount))
      const newG = Math.max(0, Math.min(255, g + amount))
      const newB = Math.max(0, Math.min(255, b + amount))

      // 转换回十六进制
      return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`
    },

    // 图片加载错误处理
    handleImageError() {
      console.error('微信二维码图片加载失败')
      this.imageLoadError = true
    },

    // 图片加载成功处理
    handleImageLoad() {
      this.imageLoadError = false
    },

    // 跳转到中奖记录页面
    goToRecords() {
      uni.navigateTo({
        url: `/pages/records/records?merchantCode=${this.merchantCode}&userOpenid=${this.userOpenid}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.lottery-container {
  min-height: 100vh;
  /* 背景色通过内联样式动态设置 */
  padding: 40rpx 30rpx;
  padding-bottom: 80rpx;
  /* 增加底部间距，确保内容不被遮挡 */
  box-sizing: border-box;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;

  .loading-icon {
    font-size: 80rpx;
    margin-bottom: 30rpx;
    animation: rotate 2s linear infinite;
  }

  .loading-text {
    font-size: 32rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.merchant-header {
  text-align: center;
  margin-bottom: 60rpx;

  .merchant-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 10rpx;
  }

  .activity-desc {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    padding: 0 20rpx;
  }
}



.lottery-grid-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;

  .grid-wrapper {
    width: 600rpx;
    height: 600rpx;
  }

  .lottery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 8rpx;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    padding: 20rpx;
    box-sizing: border-box;
  }

  .grid-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    &.active {
      background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
      transform: scale(1.05);
      box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);

      .prize-item {
        .prize-icon {
          animation: bounce 0.6s ease-in-out;
        }

        .prize-name {
          color: #fff;
          font-weight: bold;
        }
      }
    }

    &.center {
      /* 背景色通过内联样式动态设置 */
      cursor: pointer;

      &:hover {
        transform: scale(1.02);
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }

  .prize-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 10rpx;

    .prize-icon {
      font-size: 48rpx;
      margin-bottom: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;

      .prize-image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }

      .default-icon {
        font-size: 48rpx;
      }
    }

    .prize-name {
      font-size: 24rpx;
      color: #333;
      line-height: 1.2;
      word-break: break-all;
    }
  }

  .center-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #fff;

    .center-text {
      font-size: 28rpx;
      font-weight: bold;
      margin-bottom: 8rpx;
    }

    .remaining-text {
      font-size: 20rpx;
      opacity: 0.9;
    }
  }
}

/* 上次中奖记录页面内显示 */
.last-winning-section {
  margin-bottom: 40rpx;
}

.last-winning-card {
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin: 0 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;

  .title-icon {
    font-size: 40rpx;
    margin-right: 15rpx;
  }

  .title-text {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.prize-info-card {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;

  .prize-icon-large {
    width: 100rpx;
    height: 100rpx;
    margin-right: 25rpx;
    border-radius: 15rpx;
    overflow: hidden;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .prize-image-large {
      width: 80rpx;
      height: 80rpx;
      border-radius: 10rpx;
    }

    .default-icon-large {
      font-size: 60rpx;
    }
  }

  .prize-details-large {
    flex: 1;

    .prize-name-large {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }

    .prize-desc-large {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 10rpx;
      line-height: 1.4;
    }

    .draw-time-large {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 10rpx;
    }

    .claim-status {
      display: inline-block;
      padding: 6rpx 12rpx;
      border-radius: 15rpx;
      font-size: 22rpx;
      font-weight: 500;

      &.claimed {
        background: linear-gradient(135deg, #2ed573, #7bed9f);
        color: white;
      }

      &.unclaimed {
        background: linear-gradient(135deg, #ffa726, #ffcc02);
        color: white;
      }
    }
  }
}

.claim-instruction-card {
  background: #f0f8ff;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;

  .instruction-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 15rpx;
  }

  .instruction-content {
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
  }
}

.wechat-qrcode-card {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 25rpx;
  text-align: center;
  margin-bottom: 30rpx;

  .qrcode-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .qrcode-img-inline {
    width: 150rpx;
    height: 150rpx;
    border-radius: 10rpx;
    margin-bottom: 15rpx;
  }

  .qrcode-error {
    color: #666;
    font-size: 24rpx;
  }
}

.more-records-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-align: center;
  padding: 25rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.6);
  }
}



@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10rpx);
  }

  60% {
    transform: translateY(-5rpx);
  }
}

@keyframes fireworkAnimation {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 1;
  }

  100% {
    transform: translate(100rpx, -100rpx) scale(0);
    opacity: 0;
  }
}

/* 无活动提示样式 */
.no-activity-tip {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;

  .tip-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
  }

  .tip-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .tip-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

/* 抽奖规则样式 */
.lottery-rules {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;

  .rules-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .rules-content {
    font-size: 28rpx;
    color: #666;
    line-height: 1.8;
    text-align: left;
    white-space: pre-wrap;
  }
}

/* 抽奖次数用完提示样式 */
.no-draws-tip {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;

  .tip-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
  }

  .tip-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .tip-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;

    text {
      display: block;
      margin-bottom: 10rpx;
    }
  }
}

/* 抽奖次数信息样式 */
.draws-info {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;

  .info-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .info-content {
    display: flex;
    flex-direction: column;
    gap: 15rpx;
  }

  .info-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10rpx;

    .info-label {
      font-size: 28rpx;
      color: #666;
      font-weight: 500;
    }

    .info-value {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
    }

    .info-remaining {
      font-size: 24rpx;
      /* 颜色通过内联样式动态设置 */
      font-weight: 500;
    }
  }
}

.prize-list {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;

  .prize-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .prize-items {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
  }

  .prize-item {
    flex: 1;
    min-width: 200rpx;
    background: #f8f9fa;
    border-radius: 10rpx;
    padding: 20rpx;

    .prize-info {
      display: flex;
      align-items: center;
      gap: 15rpx;
    }

    .prize-icon-small {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .prize-image-small {
        width: 100%;
        height: 100%;
        border-radius: 6rpx;
      }

      .default-icon-small {
        font-size: 32rpx;
      }
    }

    .prize-details {
      flex: 1;
      text-align: left;
    }

    .prize-name {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }

    .prize-probability {
      font-size: 24rpx;
      color: #666;
    }
  }
}
</style>