@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.lottery-container.data-v-557dc19a {
  min-height: 100vh;
  /* 背景色通过内联样式动态设置 */
  padding: 40rpx 30rpx;
  padding-bottom: 80rpx;
  /* 增加底部间距，确保内容不被遮挡 */
  box-sizing: border-box;
}
/* 加载状态样式 */
.loading-container.data-v-557dc19a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}
.loading-container .loading-icon.data-v-557dc19a {
  font-size: 80rpx;
  margin-bottom: 30rpx;
  -webkit-animation: rotate-data-v-557dc19a 2s linear infinite;
          animation: rotate-data-v-557dc19a 2s linear infinite;
}
.loading-container .loading-text.data-v-557dc19a {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
}
@-webkit-keyframes rotate-data-v-557dc19a {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes rotate-data-v-557dc19a {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.merchant-header.data-v-557dc19a {
  text-align: center;
  margin-bottom: 60rpx;
}
.merchant-header .merchant-name.data-v-557dc19a {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}
.merchant-header .activity-desc.data-v-557dc19a {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  padding: 0 20rpx;
}
.lottery-grid-container.data-v-557dc19a {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}
.lottery-grid-container .grid-wrapper.data-v-557dc19a {
  width: 600rpx;
  height: 600rpx;
}
.lottery-grid-container .lottery-grid.data-v-557dc19a {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 8rpx;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 20rpx;
  box-sizing: border-box;
}
.lottery-grid-container .grid-item.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.lottery-grid-container .grid-item.active.data-v-557dc19a {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);
}
.lottery-grid-container .grid-item.active .prize-item .prize-icon.data-v-557dc19a {
  -webkit-animation: bounce-data-v-557dc19a 0.6s ease-in-out;
          animation: bounce-data-v-557dc19a 0.6s ease-in-out;
}
.lottery-grid-container .grid-item.active .prize-item .prize-name.data-v-557dc19a {
  color: #fff;
  font-weight: bold;
}
.lottery-grid-container .grid-item.center.data-v-557dc19a {
  /* 背景色通过内联样式动态设置 */
  cursor: pointer;
}
.lottery-grid-container .grid-item.center.data-v-557dc19a:hover {
  -webkit-transform: scale(1.02);
          transform: scale(1.02);
}
.lottery-grid-container .grid-item.center.data-v-557dc19a:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.lottery-grid-container .prize-item.data-v-557dc19a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 10rpx;
}
.lottery-grid-container .prize-item .prize-icon.data-v-557dc19a {
  font-size: 48rpx;
  margin-bottom: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}
.lottery-grid-container .prize-item .prize-icon .prize-image.data-v-557dc19a {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.lottery-grid-container .prize-item .prize-icon .default-icon.data-v-557dc19a {
  font-size: 48rpx;
}
.lottery-grid-container .prize-item .prize-name.data-v-557dc19a {
  font-size: 24rpx;
  color: #333;
  line-height: 1.2;
  word-break: break-all;
}
.lottery-grid-container .center-button.data-v-557dc19a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #fff;
}
.lottery-grid-container .center-button .center-text.data-v-557dc19a {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.lottery-grid-container .center-button .remaining-text.data-v-557dc19a {
  font-size: 20rpx;
  opacity: 0.9;
}
/* 上次中奖记录页面内显示 */
.last-winning-section.data-v-557dc19a {
  margin-bottom: 40rpx;
}
.last-winning-card.data-v-557dc19a {
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin: 0 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}
.section-title.data-v-557dc19a {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}
.section-title .title-icon.data-v-557dc19a {
  font-size: 40rpx;
  margin-right: 15rpx;
}
.section-title .title-text.data-v-557dc19a {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.prize-info-card.data-v-557dc19a {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.prize-info-card .prize-icon-large.data-v-557dc19a {
  width: 100rpx;
  height: 100rpx;
  margin-right: 25rpx;
  border-radius: 15rpx;
  overflow: hidden;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.prize-info-card .prize-icon-large .prize-image-large.data-v-557dc19a {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
}
.prize-info-card .prize-icon-large .default-icon-large.data-v-557dc19a {
  font-size: 60rpx;
}
.prize-info-card .prize-details-large.data-v-557dc19a {
  flex: 1;
}
.prize-info-card .prize-details-large .prize-name-large.data-v-557dc19a {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.prize-info-card .prize-details-large .prize-desc-large.data-v-557dc19a {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.4;
}
.prize-info-card .prize-details-large .draw-time-large.data-v-557dc19a {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.prize-info-card .prize-details-large .claim-status.data-v-557dc19a {
  display: inline-block;
  padding: 6rpx 12rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.prize-info-card .prize-details-large .claim-status.claimed.data-v-557dc19a {
  background: linear-gradient(135deg, #2ed573, #7bed9f);
  color: white;
}
.prize-info-card .prize-details-large .claim-status.unclaimed.data-v-557dc19a {
  background: linear-gradient(135deg, #ffa726, #ffcc02);
  color: white;
}
.claim-instruction-card.data-v-557dc19a {
  background: #f0f8ff;
  border-radius: 15rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
}
.claim-instruction-card .instruction-title.data-v-557dc19a {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}
.claim-instruction-card .instruction-content.data-v-557dc19a {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
.wechat-qrcode-card.data-v-557dc19a {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 25rpx;
  text-align: center;
  margin-bottom: 30rpx;
}
.wechat-qrcode-card .qrcode-title.data-v-557dc19a {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.wechat-qrcode-card .qrcode-img-inline.data-v-557dc19a {
  width: 150rpx;
  height: 150rpx;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
}
.wechat-qrcode-card .qrcode-error.data-v-557dc19a {
  color: #666;
  font-size: 24rpx;
}
.more-records-btn.data-v-557dc19a {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  text-align: center;
  padding: 25rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}
.more-records-btn.data-v-557dc19a:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.6);
}
@-webkit-keyframes bounce-data-v-557dc19a {
0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
40% {
    -webkit-transform: translateY(-10rpx);
            transform: translateY(-10rpx);
}
60% {
    -webkit-transform: translateY(-5rpx);
            transform: translateY(-5rpx);
}
}
@keyframes bounce-data-v-557dc19a {
0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
40% {
    -webkit-transform: translateY(-10rpx);
            transform: translateY(-10rpx);
}
60% {
    -webkit-transform: translateY(-5rpx);
            transform: translateY(-5rpx);
}
}
@-webkit-keyframes fireworkAnimation-data-v-557dc19a {
0% {
    -webkit-transform: translate(0, 0) scale(1);
            transform: translate(0, 0) scale(1);
    opacity: 1;
}
100% {
    -webkit-transform: translate(100rpx, -100rpx) scale(0);
            transform: translate(100rpx, -100rpx) scale(0);
    opacity: 0;
}
}
@keyframes fireworkAnimation-data-v-557dc19a {
0% {
    -webkit-transform: translate(0, 0) scale(1);
            transform: translate(0, 0) scale(1);
    opacity: 1;
}
100% {
    -webkit-transform: translate(100rpx, -100rpx) scale(0);
            transform: translate(100rpx, -100rpx) scale(0);
    opacity: 0;
}
}
/* 无活动提示样式 */
.no-activity-tip.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
}
.no-activity-tip .tip-icon.data-v-557dc19a {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.no-activity-tip .tip-title.data-v-557dc19a {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.no-activity-tip .tip-desc.data-v-557dc19a {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
/* 抽奖规则样式 */
.lottery-rules.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
}
.lottery-rules .rules-title.data-v-557dc19a {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}
.lottery-rules .rules-content.data-v-557dc19a {
  font-size: 28rpx;
  color: #666;
  line-height: 1.8;
  text-align: left;
  white-space: pre-wrap;
}
/* 抽奖次数用完提示样式 */
.no-draws-tip.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  text-align: center;
}
.no-draws-tip .tip-icon.data-v-557dc19a {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.no-draws-tip .tip-title.data-v-557dc19a {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.no-draws-tip .tip-desc.data-v-557dc19a {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.no-draws-tip .tip-desc text.data-v-557dc19a {
  display: block;
  margin-bottom: 10rpx;
}
/* 抽奖次数信息样式 */
.draws-info.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.draws-info .info-title.data-v-557dc19a {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}
.draws-info .info-content.data-v-557dc19a {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}
.draws-info .info-item.data-v-557dc19a {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10rpx;
}
.draws-info .info-item .info-label.data-v-557dc19a {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}
.draws-info .info-item .info-value.data-v-557dc19a {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.draws-info .info-item .info-remaining.data-v-557dc19a {
  font-size: 24rpx;
  /* 颜色通过内联样式动态设置 */
  font-weight: 500;
}
.prize-list.data-v-557dc19a {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}
.prize-list .prize-title.data-v-557dc19a {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}
.prize-list .prize-items.data-v-557dc19a {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.prize-list .prize-item.data-v-557dc19a {
  flex: 1;
  min-width: 200rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
}
.prize-list .prize-item .prize-info.data-v-557dc19a {
  display: flex;
  align-items: center;
  gap: 15rpx;
}
.prize-list .prize-item .prize-icon-small.data-v-557dc19a {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.prize-list .prize-item .prize-icon-small .prize-image-small.data-v-557dc19a {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.prize-list .prize-item .prize-icon-small .default-icon-small.data-v-557dc19a {
  font-size: 32rpx;
}
.prize-list .prize-item .prize-details.data-v-557dc19a {
  flex: 1;
  text-align: left;
}
.prize-list .prize-item .prize-name.data-v-557dc19a {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}
.prize-list .prize-item .prize-probability.data-v-557dc19a {
  font-size: 24rpx;
  color: #666;
}
